@import 'tailwindcss';
@import 'tw-animate-css';
/* Individual theme variants - themes from  https://github.com/rawestmoreland/theme-generator/blob/main/src/app/globals.css */

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5, --input);
  --color-chart-4: var(--chart-4, --input);
  --color-chart-3: var(--chart-3, --input);
  --color-chart-2: var(--chart-2, --input);
  --color-chart-1: var(--chart-1, --input);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: hsl(221.2 83.2% 53.3%);
  --chart-2: hsl(212 95% 68%);
  --chart-3: hsl(216 92% 60%);
  --chart-4: hsl(210 98% 78%);
  --chart-5: hsl(212 97% 87%);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

[data-theme='default-dark'] {
  color-scheme: dark;
  --background: oklch(0.19 0.004 285.885);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --sidebar: oklch(0.274 0.006 286.033);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(1 0 0 / 15%);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

/* Zinc Light */
[data-theme='zinc'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(240 10% 3.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(240 10% 3.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(240 10% 3.9%);
  --primary: hsl(240 5.9% 10%);
  --primary-foreground: hsl(0 0% 98%);
  --secondary: hsl(240 4.8% 95.9%);
  --secondary-foreground: hsl(240 5.9% 10%);
  --muted: hsl(240 4.8% 95.9%);
  --muted-foreground: hsl(240 3.8% 46.1%);
  --accent: hsl(240 4.8% 95.9%);
  --accent-foreground: hsl(240 5.9% 10%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(240 5.9% 90%);
  --input: hsl(240 5.9% 90%);
  --ring: hsl(240 5.9% 10%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(240 4.8% 98%);
  --sidebar-foreground: hsl(240 10% 3.9%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(240 5.9% 90%);
  --sidebar-ring: hsl(240 5.9% 10%);
}

/* Zinc Dark */
[data-theme='zinc-dark'] {
  color-scheme: dark;
  --background: hsl(240 10% 3.9%);
  --foreground: hsl(0 0% 98%);
  --card: hsl(240 10% 3.9%);
  --card-foreground: hsl(0 0% 98%);
  --popover: hsl(240 10% 3.9%);
  --popover-foreground: hsl(0 0% 98%);
  --primary: hsl(0 0% 98%);
  --primary-foreground: hsl(240 5.9% 10%);
  --secondary: hsl(240 3.7% 15.9%);
  --secondary-foreground: hsl(0 0% 98%);
  --muted: hsl(240 3.7% 15.9%);
  --muted-foreground: hsl(240 5% 64.9%);
  --accent: hsl(240 3.7% 15.9%);
  --accent-foreground: hsl(0 0% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(240 3.7% 15.9%);
  --input: hsl(240 3.7% 15.9%);
  --ring: hsl(240 4.9% 83.9%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(240 10% 6%);
  --sidebar-foreground: hsl(0 0% 98%);
  --sidebar-primary: hsl(0 0% 98%);
  --sidebar-primary-foreground: hsl(240 5.9% 10%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(0 0% 98%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(240 4.9% 83.9%);
}

/* Slate light */
[data-theme='slate'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  --primary: hsl(222.2 47.4% 11.2%);
  --primary-foreground: hsl(210 40% 98%);
  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);
  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --ring: hsl(222.2 84% 4.9%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(210 40% 98%);
  --sidebar-foreground: hsl(222.2 84% 4.9%);
  --sidebar-primary: hsl(222.2 47.4% 11.2%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(210 40% 96.1%);
  --sidebar-accent-foreground: hsl(222.2 47.4% 11.2%);
  --sidebar-border: hsl(214.3 31.8% 91.4%);
  --sidebar-ring: hsl(222.2 84% 4.9%);
}

/* Slate Dark */
[data-theme='slate-dark'] {
  color-scheme: dark;
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);
  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);
  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);
  --primary: hsl(210 40% 98%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);
  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);
  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: hsl(212.7 26.8% 83.9%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(222.2 84% 7%);
  --sidebar-foreground: hsl(210 40% 98%);
  --sidebar-primary: hsl(210 40% 98%);
  --sidebar-primary-foreground: hsl(222.2 47.4% 11.2%);
  --sidebar-accent: hsl(217.2 32.6% 17.5%);
  --sidebar-accent-foreground: hsl(210 40% 98%);
  --sidebar-border: hsl(217.2 32.6% 17.5%);
  --sidebar-ring: hsl(212.7 26.8% 83.9%);
}

/* Stone Light */
[data-theme='stone'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(28 25% 8%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(28 25% 8%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(28 25% 8%);
  --primary: hsl(28 25% 15%);
  --primary-foreground: hsl(60 9.1% 97.8%);
  --secondary: hsl(60 4.8% 95.9%);
  --secondary-foreground: hsl(28 25% 15%);
  --muted: hsl(60 4.8% 95.9%);
  --muted-foreground: hsl(25 5.3% 44.7%);
  --accent: hsl(60 4.8% 95.9%);
  --accent-foreground: hsl(28 25% 15%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(60 9.1% 97.8%);
  --border: hsl(20 5.9% 90%);
  --input: hsl(20 5.9% 90%);
  --ring: hsl(28 25% 8%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(60 4.8% 98%);
  --sidebar-foreground: hsl(28 25% 8%);
  --sidebar-primary: hsl(28 25% 15%);
  --sidebar-primary-foreground: hsl(60 9.1% 97.8%);
  --sidebar-accent: hsl(60 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(28 25% 15%);
  --sidebar-border: hsl(20 5.9% 90%);
  --sidebar-ring: hsl(28 25% 8%);
}

/* Stone Dark */
[data-theme='stone-dark'] {
  color-scheme: dark;
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(60 9.1% 97.8%);
  --card: hsl(20 14.3% 4.1%);
  --card-foreground: hsl(60 9.1% 97.8%);
  --popover: hsl(20 14.3% 4.1%);
  --popover-foreground: hsl(60 9.1% 97.8%);
  --primary: hsl(20.5 90.2% 48.2%);
  --primary-foreground: hsl(60 9.1% 97.8%);
  --secondary: hsl(12 6.5% 15.1%);
  --secondary-foreground: hsl(60 9.1% 97.8%);
  --muted: hsl(12 6.5% 15.1%);
  --muted-foreground: hsl(24 5.4% 63.9%);
  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(60 9.1% 97.8%);
  --destructive: hsl(0 72.2% 50.6%);
  --destructive-foreground: hsl(60 9.1% 97.8%);
  --border: hsl(12 6.5% 15.1%);
  --input: hsl(12 6.5% 15.1%);
  --ring: hsl(24 5.7% 82.9%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(20 14.3% 6%);
  --sidebar-foreground: hsl(60 9.1% 97.8%);
  --sidebar-primary: hsl(60 9.1% 97.8%);
  --sidebar-primary-foreground: hsl(60 9.1% 97.8%);
  --sidebar-accent: hsl(12 6.5% 15.1%);
  --sidebar-accent-foreground: hsl(60 9.1% 97.8%);
  --sidebar-border: hsl(12 6.5% 15.1%);
  --sidebar-ring: hsl(24 5.7% 82.9%);
}

/* Gray Light */
[data-theme='gray'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(224 71.4% 4.1%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(224 71.4% 4.1%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(224 71.4% 4.1%);
  --primary: hsl(220.9 39.3% 11%);
  --primary-foreground: hsl(210 20% 98%);
  --secondary: hsl(220 14.3% 95.9%);
  --secondary-foreground: hsl(220.9 39.3% 11%);
  --muted: hsl(220 14.3% 95.9%);
  --muted-foreground: hsl(220 8.9% 46.1%);
  --accent: hsl(220 14.3% 95.9%);
  --accent-foreground: hsl(220.9 39.3% 11%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);
  --ring: hsl(224 71.4% 4.1%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(220 14.3% 98%);
  --sidebar-foreground: hsl(224 71.4% 4.1%);
  --sidebar-primary: hsl(220.9 39.3% 11%);
  --sidebar-primary-foreground: hsl(210 20% 98%);
  --sidebar-accent: hsl(220 14.3% 95.9%);
  --sidebar-accent-foreground: hsl(220.9 39.3% 11%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(224 71.4% 4.1%);
}

/* Gray Dark */
[data-theme='gray-dark'] {
  color-scheme: dark;
  --background: hsl(224 71.4% 4.1%);
  --foreground: hsl(210 20% 98%);
  --card: hsl(224 71.4% 4.1%);
  --card-foreground: hsl(210 20% 98%);
  --popover: hsl(224 71.4% 4.1%);
  --popover-foreground: hsl(210 20% 98%);
  --primary: hsl(210 20% 98%);
  --primary-foreground: hsl(220.9 39.3% 11%);
  --secondary: hsl(215 27.9% 16.9%);
  --secondary-foreground: hsl(210 20% 98%);
  --muted: hsl(215 27.9% 16.9%);
  --muted-foreground: hsl(217.9 10.6% 64.9%);
  --accent: hsl(215 27.9% 16.9%);
  --accent-foreground: hsl(210 20% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 20% 98%);
  --border: hsl(215 27.9% 16.9%);
  --input: hsl(215 27.9% 16.9%);
  --ring: hsl(216 12.2% 83.9%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(224 71.4% 6%);
  --sidebar-foreground: hsl(210 20% 98%);
  --sidebar-primary: hsl(210 20% 98%);
  --sidebar-primary-foreground: hsl(220.9 39.3% 11%);
  --sidebar-accent: hsl(215 27.9% 16.9%);
  --sidebar-accent-foreground: hsl(210 20% 98%);
  --sidebar-border: hsl(215 27.9% 16.9%);
  --sidebar-ring: hsl(216 12.2% 83.9%);
}

/* Blue Light */
[data-theme='blue'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  --primary: hsl(221.2 83.2% 53.3%);
  --primary-foreground: hsl(210 40% 98%);
  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);
  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --ring: hsl(221.2 83.2% 53.3%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(210 40% 98%);
  --sidebar-foreground: hsl(222.2 84% 4.9%);
  --sidebar-primary: hsl(221.2 83.2% 53.3%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(210 40% 96.1%);
  --sidebar-accent-foreground: hsl(222.2 47.4% 11.2%);
  --sidebar-border: hsl(214.3 31.8% 91.4%);
  --sidebar-ring: hsl(221.2 83.2% 53.3%);
}

/* Blue Dark */
[data-theme='blue-dark'] {
  color-scheme: dark;
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);
  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);
  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);
  --primary: hsl(217.2 91.2% 59.8%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);
  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);
  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: hsl(224.3 76.3% 48%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(222.2 84% 7%);
  --sidebar-foreground: hsl(210 40% 98%);
  --sidebar-primary: hsl(217.2 91.2% 59.8%);
  --sidebar-primary-foreground: hsl(222.2 47.4% 11.2%);
  --sidebar-accent: hsl(217.2 32.6% 17.5%);
  --sidebar-accent-foreground: hsl(210 40% 98%);
  --sidebar-border: hsl(217.2 32.6% 17.5%);
  --sidebar-ring: hsl(224.3 76.3% 48%);
}

/* Orange Light */
[data-theme='orange'] {
  --background: hsl(0 0% 100%);
  --foreground: hsl(20 14.3% 4.1%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(20 14.3% 4.1%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(20 14.3% 4.1%);
  --primary: hsl(24.6 95% 53.1%);
  --primary-foreground: hsl(60 9.1% 97.8%);
  --secondary: hsl(60 4.8% 95.9%);
  --secondary-foreground: hsl(24 9.8% 10%);
  --muted: hsl(60 4.8% 95.9%);
  --muted-foreground: hsl(25 5.3% 44.7%);
  --accent: hsl(60 4.8% 95.9%);
  --accent-foreground: hsl(24 9.8% 10%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(60 9.1% 97.8%);
  --border: hsl(20 5.9% 90%);
  --input: hsl(20 5.9% 90%);
  --ring: hsl(24.6 95% 53.1%);
  --radius: 0.6rem;
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(60 4.8% 98%);
  --sidebar-foreground: hsl(20 14.3% 4.1%);
  --sidebar-primary: hsl(24.6 95% 53.1%);
  --sidebar-primary-foreground: hsl(60 9.1% 97.8%);
  --sidebar-accent: hsl(60 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(24 9.8% 10%);
  --sidebar-border: hsl(20 5.9% 90%);
  --sidebar-ring: hsl(24.6 95% 53.1%);
}

/* Orange Dark */
[data-theme='orange-dark'] {
  color-scheme: dark;
  --background: hsl(20 14.3% 4.1%);
  --foreground: hsl(60 9.1% 97.8%);
  --card: hsl(20 14.3% 4.1%);
  --card-foreground: hsl(60 9.1% 97.8%);
  --popover: hsl(20 14.3% 4.1%);
  --popover-foreground: hsl(60 9.1% 97.8%);
  --primary: hsl(20.5 90.2% 48.2%);
  --primary-foreground: hsl(60 9.1% 97.8%);
  --secondary: hsl(12 6.5% 15.1%);
  --secondary-foreground: hsl(60 9.1% 97.8%);
  --muted: hsl(12 6.5% 15.1%);
  --muted-foreground: hsl(24 5.4% 63.9%);
  --accent: hsl(12 6.5% 15.1%);
  --accent-foreground: hsl(60 9.1% 97.8%);
  --destructive: hsl(0 72.2% 50.6%);
  --destructive-foreground: hsl(60 9.1% 97.8%);
  --border: hsl(12 6.5% 15.1%);
  --input: hsl(12 6.5% 15.1%);
  --ring: hsl(20.5 90.2% 48.2%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(20 14.3% 6%);
  --sidebar-foreground: hsl(60 9.1% 97.8%);
  --sidebar-primary: hsl(20.5 90.2% 48.2%);
  --sidebar-primary-foreground: hsl(60 9.1% 97.8%);
  --sidebar-accent: hsl(12 6.5% 15.1%);
  --sidebar-accent-foreground: hsl(60 9.1% 97.8%);
  --sidebar-border: hsl(12 6.5% 15.1%);
  --sidebar-ring: hsl(20.5 90.2% 48.2%);
}

/* Bubblegum Pop Light */
[data-theme='bubblegum-pop'] {
  --background: hsl(330 100% 99%);
  --foreground: hsl(330 10% 10%);
  --card: hsl(330 100% 98%);
  --card-foreground: hsl(330 10% 10%);
  --popover: hsl(330 100% 98%);
  --popover-foreground: hsl(330 10% 10%);
  --primary: hsl(330 90% 60%);
  --primary-foreground: hsl(330 10% 98%);
  --secondary: hsl(275 90% 80%);
  --secondary-foreground: hsl(275 10% 10%);
  --muted: hsl(330 20% 90%);
  --muted-foreground: hsl(330 10% 40%);
  --accent: hsl(30 90% 80%);
  --accent-foreground: hsl(30 10% 10%);
  --destructive: hsl(0 90% 60%);
  --destructive-foreground: hsl(0 10% 98%);
  --border: hsl(330 60% 90%);
  --input: hsl(330 60% 90%);
  --ring: hsl(330 90% 60%);
  --radius: 1rem;
  --chart-1: hsl(330 90% 60%);
  --chart-2: hsl(275 90% 60%);
  --chart-3: hsl(30 90% 60%);
  --chart-4: hsl(180 90% 60%);
  --chart-5: hsl(60 90% 60%);
  --sidebar: hsl(330 80% 95%);
  --sidebar-foreground: hsl(330 10% 10%);
  --sidebar-primary: hsl(330 90% 60%);
  --sidebar-primary-foreground: hsl(330 10% 98%);
  --sidebar-accent: hsl(275 90% 80%);
  --sidebar-accent-foreground: hsl(275 10% 10%);
  --sidebar-border: hsl(330 60% 85%);
  --sidebar-ring: hsl(330 90% 60%);
}

/* Bubblegum Pop Dark */
[data-theme='bubblegum-pop-dark'] {
  color-scheme: dark;
  --background: hsl(330 50% 10%);
  --foreground: hsl(330 10% 95%);
  --card: hsl(330 50% 12%);
  --card-foreground: hsl(330 10% 95%);
  --popover: hsl(330 50% 12%);
  --popover-foreground: hsl(330 10% 95%);
  --primary: hsl(330 90% 60%);
  --primary-foreground: hsl(330 10% 98%);
  --secondary: hsl(275 90% 40%);
  --secondary-foreground: hsl(275 10% 98%);
  --muted: hsl(330 30% 20%);
  --muted-foreground: hsl(330 10% 70%);
  --accent: hsl(30 90% 40%);
  --accent-foreground: hsl(30 10% 98%);
  --destructive: hsl(0 90% 40%);
  --destructive-foreground: hsl(0 10% 98%);
  --border: hsl(330 60% 20%);
  --input: hsl(330 60% 20%);
  --ring: hsl(330 90% 60%);
  --radius: 1rem;
  --chart-1: hsl(330 90% 60%);
  --chart-2: hsl(275 90% 60%);
  --chart-3: hsl(30 90% 60%);
  --chart-4: hsl(180 90% 60%);
  --chart-5: hsl(60 90% 60%);
  --sidebar: hsl(330 50% 15%);
  --sidebar-foreground: hsl(330 10% 95%);
  --sidebar-primary: hsl(330 90% 60%);
  --sidebar-primary-foreground: hsl(330 10% 98%);
  --sidebar-accent: hsl(275 90% 40%);
  --sidebar-accent-foreground: hsl(275 10% 98%);
  --sidebar-border: hsl(330 60% 25%);
  --sidebar-ring: hsl(330 90% 60%);
}

/* Cyberpunk Neon Light */
[data-theme='cyberpunk-neon'] {
  --background: hsl(220 20% 97%);
  --foreground: hsl(220 80% 5%);
  --card: hsl(220 20% 98%);
  --card-foreground: hsl(220 80% 5%);
  --popover: hsl(220 20% 98%);
  --popover-foreground: hsl(220 80% 5%);
  --primary: hsl(320 100% 50%);
  --primary-foreground: hsl(320 100% 98%);
  --secondary: hsl(180 100% 50%);
  --secondary-foreground: hsl(180 100% 10%);
  --muted: hsl(220 20% 90%);
  --muted-foreground: hsl(220 20% 40%);
  --accent: hsl(65 100% 50%);
  --accent-foreground: hsl(65 100% 10%);
  --destructive: hsl(0 100% 50%);
  --destructive-foreground: hsl(0 100% 98%);
  --border: hsl(220 20% 80%);
  --input: hsl(220 20% 80%);
  --ring: hsl(320 100% 50%);
  --radius: 0.125rem;
  --chart-1: hsl(320 100% 50%);
  --chart-2: hsl(180 100% 50%);
  --chart-3: hsl(65 100% 50%);
  --chart-4: hsl(260 100% 50%);
  --chart-5: hsl(30 100% 50%);
  --sidebar: hsl(220 20% 95%);
  --sidebar-foreground: hsl(220 80% 5%);
  --sidebar-primary: hsl(320 100% 50%);
  --sidebar-primary-foreground: hsl(320 100% 98%);
  --sidebar-accent: hsl(180 100% 50%);
  --sidebar-accent-foreground: hsl(180 100% 10%);
  --sidebar-border: hsl(220 20% 85%);
  --sidebar-ring: hsl(320 100% 50%);
}

/* Cyberpunk Neon Dark */
[data-theme='cyberpunk-neon-dark'] {
  color-scheme: dark;
  --background: hsl(220 80% 5%);
  --foreground: hsl(220 20% 98%);
  --card: hsl(220 80% 7%);
  --card-foreground: hsl(220 20% 98%);
  --popover: hsl(220 80% 7%);
  --popover-foreground: hsl(220 20% 98%);
  --primary: hsl(320 100% 60%);
  --primary-foreground: hsl(320 100% 10%);
  --secondary: hsl(180 100% 60%);
  --secondary-foreground: hsl(180 100% 10%);
  --muted: hsl(220 80% 20%);
  --muted-foreground: hsl(220 20% 70%);
  --accent: hsl(65 100% 60%);
  --accent-foreground: hsl(65 100% 10%);
  --destructive: hsl(0 100% 60%);
  --destructive-foreground: hsl(0 100% 10%);
  --border: hsl(220 80% 30%);
  --input: hsl(220 80% 30%);
  --radius: 0.125rem;
  --ring: hsl(320 100% 60%);
  --chart-1: hsl(320 100% 60%);
  --chart-2: hsl(180 100% 60%);
  --chart-3: hsl(65 100% 60%);
  --chart-4: hsl(260 100% 60%);
  --chart-5: hsl(30 100% 60%);
  --sidebar: hsl(220 80% 8%);
  --sidebar-foreground: hsl(220 20% 98%);
  --sidebar-primary: hsl(320 100% 60%);
  --sidebar-primary-foreground: hsl(320 100% 10%);
  --sidebar-accent: hsl(180 100% 60%);
  --sidebar-accent-foreground: hsl(180 100% 10%);
  --sidebar-border: hsl(220 80% 35%);
  --sidebar-ring: hsl(320 100% 60%);
}

/* Retro Arcade Light */
[data-theme='retro-arcade'] {
  --background: hsl(60 10% 95%);
  --foreground: hsl(60 10% 5%);
  --card: hsl(60 10% 97%);
  --card-foreground: hsl(60 10% 5%);
  --popover: hsl(60 10% 97%);
  --popover-foreground: hsl(60 10% 5%);
  --primary: hsl(220 90% 50%);
  --primary-foreground: hsl(220 90% 98%);
  --secondary: hsl(120 90% 40%);
  --secondary-foreground: hsl(120 90% 98%);
  --muted: hsl(60 10% 85%);
  --muted-foreground: hsl(60 10% 45%);
  --accent: hsl(30 90% 50%);
  --accent-foreground: hsl(30 90% 98%);
  --destructive: hsl(0 90% 50%);
  --destructive-foreground: hsl(0 90% 98%);
  --border: hsl(60 10% 75%);
  --input: hsl(60 10% 75%);
  --ring: hsl(220 90% 50%);
  --radius: 0;
  --chart-1: hsl(220 90% 50%);
  --chart-2: hsl(120 90% 40%);
  --chart-3: hsl(30 90% 50%);
  --chart-4: hsl(280 90% 50%);
  --chart-5: hsl(180 90% 40%);
  --sidebar: hsl(60 10% 90%);
  --sidebar-foreground: hsl(60 10% 5%);
  --sidebar-primary: hsl(220 90% 50%);
  --sidebar-primary-foreground: hsl(220 90% 98%);
  --sidebar-accent: hsl(120 90% 40%);
  --sidebar-accent-foreground: hsl(120 90% 98%);
  --sidebar-border: hsl(60 10% 80%);
  --sidebar-ring: hsl(220 90% 50%);
}

/* Retro Arcade Dark */
[data-theme='retro-arcade-dark'] {
  color-scheme: dark;
  --background: hsl(240 10% 5%);
  --foreground: hsl(60 10% 95%);
  --card: hsl(240 10% 7%);
  --card-foreground: hsl(60 10% 95%);
  --popover: hsl(240 10% 7%);
  --popover-foreground: hsl(60 10% 95%);
  --primary: hsl(220 90% 60%);
  --primary-foreground: hsl(220 90% 10%);
  --secondary: hsl(120 90% 50%);
  --secondary-foreground: hsl(120 90% 10%);
  --muted: hsl(240 10% 20%);
  --muted-foreground: hsl(60 10% 75%);
  --accent: hsl(30 90% 60%);
  --accent-foreground: hsl(30 90% 10%);
  --destructive: hsl(0 90% 60%);
  --destructive-foreground: hsl(0 90% 10%);
  --border: hsl(240 10% 30%);
  --input: hsl(240 10% 30%);
  --radius: 0;
  --ring: hsl(220 90% 60%);
  --chart-1: hsl(220 90% 60%);
  --chart-2: hsl(120 90% 50%);
  --chart-3: hsl(30 90% 60%);
  --chart-4: hsl(280 90% 60%);
  --chart-5: hsl(180 90% 50%);
  --sidebar: hsl(240 10% 8%);
  --sidebar-foreground: hsl(60 10% 95%);
  --sidebar-primary: hsl(220 90% 60%);
  --sidebar-primary-foreground: hsl(220 90% 10%);
  --sidebar-accent: hsl(120 90% 50%);
  --sidebar-accent-foreground: hsl(120 90% 10%);
  --sidebar-border: hsl(240 10% 35%);
  --sidebar-ring: hsl(220 90% 60%);
}

/* Tropical Paradise Light */
[data-theme='tropical-paradise'] {
  --background: hsl(180 50% 97%);
  --foreground: hsl(180 50% 10%);
  --card: hsl(180 50% 98%);
  --card-foreground: hsl(180 50% 10%);
  --popover: hsl(180 50% 98%);
  --popover-foreground: hsl(180 50% 10%);
  --primary: hsl(150 80% 40%);
  --primary-foreground: hsl(150 80% 98%);
  --secondary: hsl(35 90% 50%);
  --secondary-foreground: hsl(35 90% 10%);
  --muted: hsl(180 30% 90%);
  --muted-foreground: hsl(180 30% 40%);
  --accent: hsl(330 70% 60%);
  --accent-foreground: hsl(330 70% 10%);
  --destructive: hsl(0 90% 60%);
  --destructive-foreground: hsl(0 90% 98%);
  --border: hsl(180 50% 85%);
  --input: hsl(180 50% 85%);
  --ring: hsl(150 80% 40%);
  --radius: 0.75rem;
  --chart-1: hsl(150 80% 40%);
  --chart-2: hsl(35 90% 50%);
  --chart-3: hsl(330 70% 60%);
  --chart-4: hsl(200 90% 50%);
  --chart-5: hsl(50 90% 50%);
  --sidebar: hsl(180 50% 95%);
  --sidebar-foreground: hsl(180 50% 10%);
  --sidebar-primary: hsl(150 80% 40%);
  --sidebar-primary-foreground: hsl(150 80% 98%);
  --sidebar-accent: hsl(35 90% 50%);
  --sidebar-accent-foreground: hsl(35 90% 10%);
  --sidebar-border: hsl(180 50% 90%);
  --sidebar-ring: hsl(150 80% 40%);
}
/* Tropical Paradise Dark */
[data-theme='tropical-paradise-dark'] {
  color-scheme: dark;
  --background: hsl(200 70% 10%);
  --foreground: hsl(180 50% 97%);
  --card: hsl(200 70% 12%);
  --card-foreground: hsl(180 50% 97%);
  --popover: hsl(200 70% 12%);
  --popover-foreground: hsl(180 50% 97%);
  --primary: hsl(150 80% 50%);
  --primary-foreground: hsl(150 80% 10%);
  --secondary: hsl(35 90% 60%);
  --secondary-foreground: hsl(35 90% 10%);
  --muted: hsl(200 50% 20%);
  --muted-foreground: hsl(180 30% 80%);
  --accent: hsl(330 70% 70%);
  --accent-foreground: hsl(330 70% 10%);
  --destructive: hsl(0 90% 70%);
  --destructive-foreground: hsl(0 90% 10%);
  --border: hsl(200 70% 30%);
  --input: hsl(200 70% 30%);
  --ring: hsl(150 80% 50%);
  --radius: 0.75rem;
  --chart-1: hsl(150 80% 50%);
  --chart-2: hsl(35 90% 60%);
  --chart-3: hsl(330 70% 70%);
  --chart-4: hsl(200 90% 60%);
  --chart-5: hsl(50 90% 60%);
  --sidebar: hsl(200 70% 15%);
  --sidebar-foreground: hsl(180 50% 97%);
  --sidebar-primary: hsl(150 80% 50%);
  --sidebar-primary-foreground: hsl(150 80% 10%);
  --sidebar-accent: hsl(35 90% 60%);
  --sidebar-accent-foreground: hsl(35 90% 10%);
  --sidebar-border: hsl(200 70% 35%);
  --sidebar-ring: hsl(150 80% 50%);
}

/* Steampunk Cogs Light */
[data-theme='steampunk-cogs'] {
  --background: hsl(30 20% 95%);
  --foreground: hsl(30 20% 10%);
  --card: hsl(30 20% 97%);
  --card-foreground: hsl(30 20% 10%);
  --popover: hsl(30 20% 97%);
  --popover-foreground: hsl(30 20% 10%);
  --primary: hsl(25 80% 40%);
  --primary-foreground: hsl(25 80% 98%);
  --secondary: hsl(45 70% 50%);
  --secondary-foreground: hsl(45 70% 10%);
  --muted: hsl(30 15% 85%);
  --muted-foreground: hsl(30 15% 40%);
  --accent: hsl(15 80% 50%);
  --accent-foreground: hsl(15 80% 10%);
  --destructive: hsl(0 80% 50%);
  --destructive-foreground: hsl(0 80% 98%);
  --border: hsl(30 20% 80%);
  --input: hsl(30 20% 80%);
  --ring: hsl(25 80% 40%);
  --radius: 0.25rem;
  --chart-1: hsl(25 80% 40%);
  --chart-2: hsl(45 70% 50%);
  --chart-3: hsl(15 80% 50%);
  --chart-4: hsl(35 80% 40%);
  --chart-5: hsl(55 70% 50%);
  --sidebar: hsl(30 20% 92%);
  --sidebar-foreground: hsl(30 20% 10%);
  --sidebar-primary: hsl(25 80% 40%);
  --sidebar-primary-foreground: hsl(25 80% 98%);
  --sidebar-accent: hsl(45 70% 50%);
  --sidebar-accent-foreground: hsl(45 70% 10%);
  --sidebar-border: hsl(30 20% 85%);
  --sidebar-ring: hsl(25 80% 40%);
}

/* Steampunk Cogs Dark */
[data-theme='steampunk-cogs-dark'] {
  color-scheme: dark;
  --background: hsl(30 30% 10%);
  --foreground: hsl(30 20% 95%);
  --card: hsl(30 30% 12%);
  --card-foreground: hsl(30 20% 95%);
  --popover: hsl(30 30% 12%);
  --popover-foreground: hsl(30 20% 95%);
  --primary: hsl(25 80% 50%);
  --primary-foreground: hsl(25 80% 10%);
  --secondary: hsl(45 70% 60%);
  --secondary-foreground: hsl(45 70% 10%);
  --muted: hsl(30 25% 25%);
  --muted-foreground: hsl(30 15% 70%);
  --accent: hsl(15 80% 60%);
  --accent-foreground: hsl(15 80% 10%);
  --destructive: hsl(0 80% 60%);
  --destructive-foreground: hsl(0 80% 10%);
  --border: hsl(30 30% 30%);
  --input: hsl(30 30% 30%);
  --ring: hsl(25 80% 50%);
  --radius: 0.25rem;
  --chart-1: hsl(25 80% 50%);
  --chart-2: hsl(45 70% 60%);
  --chart-3: hsl(15 80% 60%);
  --chart-4: hsl(35 80% 50%);
  --chart-5: hsl(55 70% 60%);
  --sidebar: hsl(30 30% 15%);
  --sidebar-foreground: hsl(30 20% 95%);
  --sidebar-primary: hsl(25 80% 50%);
  --sidebar-primary-foreground: hsl(25 80% 10%);
  --sidebar-accent: hsl(45 70% 60%);
  --sidebar-accent-foreground: hsl(45 70% 10%);
  --sidebar-border: hsl(30 30% 35%);
  --sidebar-ring: hsl(25 80% 50%);
}

/* Neon Synthwave Light */
[data-theme='neon-synthwave'] {
  --background: hsl(280 30% 95%);
  --foreground: hsl(280 30% 10%);
  --card: hsl(280 30% 97%);
  --card-foreground: hsl(280 30% 10%);
  --popover: hsl(280 30% 97%);
  --popover-foreground: hsl(280 30% 10%);
  --primary: hsl(320 100% 60%);
  --primary-foreground: hsl(320 100% 98%);
  --secondary: hsl(220 100% 60%);
  --secondary-foreground: hsl(220 100% 98%);
  --muted: hsl(280 20% 90%);
  --muted-foreground: hsl(280 20% 40%);
  --accent: hsl(180 100% 50%);
  --accent-foreground: hsl(180 100% 10%);
  --destructive: hsl(0 100% 60%);
  --destructive-foreground: hsl(0 100% 98%);
  --border: hsl(280 30% 80%);
  --input: hsl(280 30% 80%);
  --ring: hsl(320 100% 60%);
  --radius: 0.6rem;
  --chart-1: hsl(320 100% 60%);
  --chart-2: hsl(220 100% 60%);
  --chart-3: hsl(180 100% 50%);
  --chart-4: hsl(260 100% 60%);
  --chart-5: hsl(300 100% 60%);
  --sidebar: hsl(280 30% 92%);
  --sidebar-foreground: hsl(280 30% 10%);
  --sidebar-primary: hsl(320 100% 60%);
  --sidebar-primary-foreground: hsl(320 100% 98%);
  --sidebar-accent: hsl(220 100% 60%);
  --sidebar-accent-foreground: hsl(220 100% 98%);
  --sidebar-border: hsl(280 30% 85%);
  --sidebar-ring: hsl(320 100% 60%);
}

/* Neon Synthwave Dark */
[data-theme='neon-synthwave-dark'] {
  color-scheme: dark;
  --background: hsl(280 50% 5%);
  --foreground: hsl(280 30% 95%);
  --card: hsl(280 50% 7%);
  --card-foreground: hsl(280 30% 95%);
  --popover: hsl(280 50% 7%);
  --popover-foreground: hsl(280 30% 95%);
  --primary: hsl(320 100% 70%);
  --primary-foreground: hsl(320 100% 10%);
  --secondary: hsl(220 100% 70%);
  --secondary-foreground: hsl(220 100% 10%);
  --muted: hsl(280 30% 20%);
  --muted-foreground: hsl(280 20% 70%);
  --accent: hsl(180 100% 60%);
  --accent-foreground: hsl(180 100% 10%);
  --destructive: hsl(0 100% 70%);
  --destructive-foreground: hsl(0 100% 10%);
  --border: hsl(280 50% 30%);
  --input: hsl(280 50% 30%);
  --ring: hsl(320 100% 70%);
  --radius: 0.6rem;
  --chart-1: hsl(320 100% 70%);
  --chart-2: hsl(220 100% 70%);
  --chart-3: hsl(180 100% 60%);
  --chart-4: hsl(260 100% 70%);
  --chart-5: hsl(300 100% 70%);
  --sidebar: hsl(280 50% 8%);
  --sidebar-foreground: hsl(280 30% 95%);
  --sidebar-primary: hsl(320 100% 70%);
  --sidebar-primary-foreground: hsl(320 100% 10%);
  --sidebar-accent: hsl(220 100% 70%);
  --sidebar-accent-foreground: hsl(220 100% 10%);
  --sidebar-border: hsl(280 50% 35%);
  --sidebar-ring: hsl(320 100% 70%);
}

/* Pastel Kawaii Light */
[data-theme='pastel-kawaii'] {
  --background: hsl(60 30% 97%);
  --foreground: hsl(60 30% 10%);
  --card: hsl(60 30% 98%);
  --card-foreground: hsl(60 30% 10%);
  --popover: hsl(60 30% 98%);
  --popover-foreground: hsl(60 30% 10%);
  --primary: hsl(350 80% 80%);
  --primary-foreground: hsl(350 80% 10%);
  --secondary: hsl(180 60% 80%);
  --secondary-foreground: hsl(180 60% 10%);
  --muted: hsl(60 20% 90%);
  --muted-foreground: hsl(60 20% 40%);
  --accent: hsl(270 70% 80%);
  --accent-foreground: hsl(270 70% 10%);
  --destructive: hsl(0 80% 80%);
  --destructive-foreground: hsl(0 80% 10%);
  --border: hsl(60 30% 85%);
  --input: hsl(60 30% 85%);
  --ring: hsl(350 80% 80%);
  --radius: 1rem;
  --chart-1: hsl(350 80% 80%);
  --chart-2: hsl(180 60% 80%);
  --chart-3: hsl(270 70% 80%);
  --chart-4: hsl(120 60% 80%);
  --chart-5: hsl(30 80% 80%);
  --sidebar: hsl(60 30% 95%);
  --sidebar-foreground: hsl(60 30% 10%);
  --sidebar-primary: hsl(350 80% 80%);
  --sidebar-primary-foreground: hsl(350 80% 10%);
  --sidebar-accent: hsl(180 60% 80%);
  --sidebar-accent-foreground: hsl(180 60% 10%);
  --sidebar-border: hsl(60 30% 90%);
  --sidebar-ring: hsl(350 80% 80%);
}

/* Pastel Kawaii Dark */
[data-theme='pastel-kawaii-dark'] {
  color-scheme: dark;
  --background: hsl(270 30% 10%);
  --foreground: hsl(60 30% 97%);
  --card: hsl(270 30% 12%);
  --card-foreground: hsl(60 30% 97%);
  --popover: hsl(270 30% 12%);
  --popover-foreground: hsl(60 30% 97%);
  --primary: hsl(350 80% 70%);
  --primary-foreground: hsl(350 80% 10%);
  --secondary: hsl(180 60% 70%);
  --secondary-foreground: hsl(180 60% 10%);
  --muted: hsl(270 20% 25%);
  --muted-foreground: hsl(60 20% 70%);
  --accent: hsl(270 70% 70%);
  --accent-foreground: hsl(270 70% 10%);
  --destructive: hsl(0 80% 70%);
  --destructive-foreground: hsl(0 80% 10%);
  --border: hsl(270 30% 30%);
  --input: hsl(270 30% 30%);
  --ring: hsl(350 80% 70%);
  --radius: 1rem;
  --chart-1: hsl(350 80% 70%);
  --chart-2: hsl(180 60% 70%);
  --chart-3: hsl(270 70% 70%);
  --chart-4: hsl(120 60% 70%);
  --chart-5: hsl(30 80% 70%);
  --sidebar: hsl(270 30% 15%);
  --sidebar-foreground: hsl(60 30% 97%);
  --sidebar-primary: hsl(350 80% 70%);
  --sidebar-primary-foreground: hsl(350 80% 10%);
  --sidebar-accent: hsl(180 60% 70%);
  --sidebar-accent-foreground: hsl(180 60% 10%);
  --sidebar-border: hsl(270 30% 35%);
  --sidebar-ring: hsl(350 80% 70%);
}

/* Space Odyssey Light */
[data-theme='space-odyssey'] {
  --background: hsl(220 20% 97%);
  --foreground: hsl(220 20% 10%);
  --card: hsl(220 20% 98%);
  --card-foreground: hsl(220 20% 10%);
  --popover: hsl(220 20% 98%);
  --popover-foreground: hsl(220 20% 10%);
  --primary: hsl(240 80% 50%);
  --primary-foreground: hsl(240 80% 98%);
  --secondary: hsl(180 70% 50%);
  --secondary-foreground: hsl(180 70% 10%);
  --muted: hsl(220 15% 90%);
  --muted-foreground: hsl(220 15% 40%);
  --accent: hsl(300 70% 50%);
  --accent-foreground: hsl(300 70% 98%);
  --destructive: hsl(0 80% 50%);
  --destructive-foreground: hsl(0 80% 98%);
  --border: hsl(220 20% 85%);
  --input: hsl(220 20% 85%);
  --ring: hsl(240 80% 50%);
  --radius: 0.375rem;
  --chart-1: hsl(240 80% 50%);
  --chart-2: hsl(180 70% 50%);
  --chart-3: hsl(300 70% 50%);
  --chart-4: hsl(60 80% 50%);
  --chart-5: hsl(120 70% 50%);
  --sidebar: hsl(220 20% 95%);
  --sidebar-foreground: hsl(220 20% 10%);
  --sidebar-primary: hsl(240 80% 50%);
  --sidebar-primary-foreground: hsl(240 80% 98%);
  --sidebar-accent: hsl(180 70% 50%);
  --sidebar-accent-foreground: hsl(180 70% 10%);
  --sidebar-border: hsl(220 20% 90%);
  --sidebar-ring: hsl(240 80% 50%);
}

/* Space Odyssey Dark */
[data-theme='space-odyssey-dark'] {
  color-scheme: dark;
  --background: hsl(230 50% 3%);
  --foreground: hsl(220 20% 97%);
  --card: hsl(230 50% 5%);
  --card-foreground: hsl(220 20% 97%);
  --popover: hsl(230 50% 5%);
  --popover-foreground: hsl(220 20% 97%);
  --primary: hsl(240 80% 60%);
  --primary-foreground: hsl(240 80% 10%);
  --secondary: hsl(180 70% 40%);
  --secondary-foreground: hsl(180 70% 98%);
  --muted: hsl(230 30% 15%);
  --muted-foreground: hsl(220 15% 70%);
  --accent: hsl(300 70% 60%);
  --accent-foreground: hsl(300 70% 10%);
  --destructive: hsl(0 80% 60%);
  --destructive-foreground: hsl(0 80% 10%);
  --border: hsl(230 50% 20%);
  --input: hsl(230 50% 20%);
  --ring: hsl(240 80% 60%);
  --radius: 0.375rem;
  --chart-1: hsl(240 80% 60%);
  --chart-2: hsl(180 70% 40%);
  --chart-3: hsl(300 70% 60%);
  --chart-4: hsl(60 80% 60%);
  --chart-5: hsl(120 70% 40%);
  --sidebar: hsl(230 50% 6%);
  --sidebar-foreground: hsl(220 20% 97%);
  --sidebar-primary: hsl(240 80% 60%);
  --sidebar-primary-foreground: hsl(240 80% 10%);
  --sidebar-accent: hsl(180 70% 40%);
  --sidebar-accent-foreground: hsl(180 70% 98%);
  --sidebar-border: hsl(230 50% 25%);
  --sidebar-ring: hsl(240 80% 60%);
}

/* Vintage Vinyl Light */
[data-theme='vintage-vinyl'] {
  --background: hsl(30 10% 98%);
  --foreground: hsl(30 10% 10%);
  --card: hsl(30 10% 99%);
  --card-foreground: hsl(30 10% 10%);
  --popover: hsl(30 10% 99%);
  --popover-foreground: hsl(30 10% 10%);
  --primary: hsl(25 20% 40%);
  --primary-foreground: hsl(25 20% 98%);
  --secondary: hsl(200 15% 70%);
  --secondary-foreground: hsl(200 15% 10%);
  --muted: hsl(30 10% 90%);
  --muted-foreground: hsl(30 10% 40%);
  --accent: hsl(340 15% 55%);
  --accent-foreground: hsl(340 15% 98%);
  --destructive: hsl(0 60% 50%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(30 10% 85%);
  --input: hsl(30 10% 85%);
  --ring: hsl(25 20% 40%);
  --radius: 0.25rem;
  --chart-1: hsl(25 20% 40%);
  --chart-2: hsl(200 15% 70%);
  --chart-3: hsl(340 15% 55%);
  --chart-4: hsl(150 15% 50%);
  --chart-5: hsl(50 20% 60%);
  --sidebar: hsl(30 10% 96%);
  --sidebar-foreground: hsl(30 10% 10%);
  --sidebar-primary: hsl(25 20% 40%);
  --sidebar-primary-foreground: hsl(25 20% 98%);
  --sidebar-accent: hsl(200 15% 70%);
  --sidebar-accent-foreground: hsl(200 15% 10%);
  --sidebar-border: hsl(30 10% 90%);
  --sidebar-ring: hsl(25 20% 40%);
}

/* Vintage Vinyl Dark */
[data-theme='vintage-vinyl-dark'] {
  color-scheme: dark;
  --background: hsl(30 15% 10%);
  --foreground: hsl(30 10% 98%);
  --card: hsl(30 15% 12%);
  --card-foreground: hsl(30 10% 98%);
  --popover: hsl(30 15% 12%);
  --popover-foreground: hsl(30 10% 98%);
  --primary: hsl(25 20% 50%);
  --primary-foreground: hsl(25 20% 10%);
  --secondary: hsl(200 15% 40%);
  --secondary-foreground: hsl(200 15% 98%);
  --muted: hsl(30 15% 20%);
  --muted-foreground: hsl(30 10% 70%);
  --accent: hsl(340 15% 45%);
  --accent-foreground: hsl(340 15% 98%);
  --destructive: hsl(0 60% 40%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(30 15% 25%);
  --input: hsl(30 15% 25%);
  --ring: hsl(25 20% 50%);
  --radius: 0.25rem;
  --chart-1: hsl(25 20% 50%);
  --chart-2: hsl(200 15% 40%);
  --chart-3: hsl(340 15% 45%);
  --chart-4: hsl(150 15% 40%);
  --chart-5: hsl(50 20% 50%);
  --sidebar: hsl(30 15% 15%);
  --sidebar-foreground: hsl(30 10% 98%);
  --sidebar-primary: hsl(25 20% 50%);
  --sidebar-primary-foreground: hsl(25 20% 10%);
  --sidebar-accent: hsl(200 15% 40%);
  --sidebar-accent-foreground: hsl(200 15% 98%);
  --sidebar-border: hsl(30 15% 30%);
  --sidebar-ring: hsl(25 20% 50%);
}

/* Zen Garden Light */
[data-theme='zen-garden'] {
  --background: hsl(90 10% 98%);
  --foreground: hsl(90 10% 10%);
  --card: hsl(90 10% 99%);
  --card-foreground: hsl(90 10% 10%);
  --popover: hsl(90 10% 99%);
  --popover-foreground: hsl(90 10% 10%);
  --primary: hsl(120 15% 45%);
  --primary-foreground: hsl(120 15% 98%);
  --secondary: hsl(60 10% 80%);
  --secondary-foreground: hsl(60 10% 10%);
  --muted: hsl(90 10% 90%);
  --muted-foreground: hsl(90 10% 40%);
  --accent: hsl(180 15% 45%);
  --accent-foreground: hsl(180 15% 98%);
  --destructive: hsl(0 60% 50%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(90 10% 85%);
  --input: hsl(90 10% 85%);
  --ring: hsl(120 15% 45%);
  --radius: 0.6rem;
  --chart-1: hsl(120 15% 45%);
  --chart-2: hsl(60 10% 80%);
  --chart-3: hsl(180 15% 45%);
  --chart-4: hsl(30 15% 50%);
  --chart-5: hsl(240 10% 60%);
  --sidebar: hsl(90 10% 96%);
  --sidebar-foreground: hsl(90 10% 10%);
  --sidebar-primary: hsl(120 15% 45%);
  --sidebar-primary-foreground: hsl(120 15% 98%);
  --sidebar-accent: hsl(60 10% 80%);
  --sidebar-accent-foreground: hsl(60 10% 10%);
  --sidebar-border: hsl(90 10% 90%);
  --sidebar-ring: hsl(120 15% 45%);
}

/* Zen Garden Dark */
[data-theme='zen-garden-dark'] {
  color-scheme: dark;
  --background: hsl(90 15% 10%);
  --foreground: hsl(90 10% 98%);
  --card: hsl(90 15% 12%);
  --card-foreground: hsl(90 10% 98%);
  --popover: hsl(90 15% 12%);
  --popover-foreground: hsl(90 10% 98%);
  --primary: hsl(120 15% 55%);
  --primary-foreground: hsl(120 15% 10%);
  --secondary: hsl(60 10% 30%);
  --secondary-foreground: hsl(60 10% 98%);
  --muted: hsl(90 15% 20%);
  --muted-foreground: hsl(90 10% 70%);
  --accent: hsl(180 15% 55%);
  --accent-foreground: hsl(180 15% 10%);
  --destructive: hsl(0 60% 40%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(90 15% 25%);
  --input: hsl(90 15% 25%);
  --ring: hsl(120 15% 55%);
  --radius: 0.6rem;
  --chart-1: hsl(120 15% 55%);
  --chart-2: hsl(60 10% 30%);
  --chart-3: hsl(180 15% 55%);
  --chart-4: hsl(30 15% 60%);
  --chart-5: hsl(240 10% 40%);
  --sidebar: hsl(90 15% 15%);
  --sidebar-foreground: hsl(90 10% 98%);
  --sidebar-primary: hsl(120 15% 55%);
  --sidebar-primary-foreground: hsl(120 15% 10%);
  --sidebar-accent: hsl(60 10% 30%);
  --sidebar-accent-foreground: hsl(60 10% 98%);
  --sidebar-border: hsl(90 15% 30%);
  --sidebar-ring: hsl(120 15% 55%);
}

/* Misty Harbor Light */
[data-theme='misty-harbor'] {
  --background: hsl(210 15% 98%);
  --foreground: hsl(210 15% 10%);
  --card: hsl(210 15% 99%);
  --card-foreground: hsl(210 15% 10%);
  --popover: hsl(210 15% 99%);
  --popover-foreground: hsl(210 15% 10%);
  --primary: hsl(200 20% 45%);
  --primary-foreground: hsl(200 20% 98%);
  --secondary: hsl(180 10% 75%);
  --secondary-foreground: hsl(180 10% 10%);
  --muted: hsl(210 15% 90%);
  --muted-foreground: hsl(210 15% 40%);
  --accent: hsl(240 15% 55%);
  --accent-foreground: hsl(240 15% 98%);
  --destructive: hsl(0 60% 50%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(210 15% 85%);
  --input: hsl(210 15% 85%);
  --ring: hsl(200 20% 45%);
  --radius: 0.375rem;
  --chart-1: 200 20% 45%;
  --chart-2: 180 10% 75%;
  --chart-3: hsl(240 15% 55%);
  --chart-4: hsl(160 20% 50%);
  --chart-5: hsl(30 15% 60%);
  --sidebar: hsl(210 15% 96%);
  --sidebar-foreground: hsl(210 15% 10%);
  --sidebar-primary: hsl(200 20% 45%);
  --sidebar-primary-foreground: hsl(200 20% 98%);
  --sidebar-accent: hsl(180 10% 75%);
  --sidebar-accent-foreground: hsl(180 10% 10%);
  --sidebar-border: hsl(210 15% 90%);
  --sidebar-ring: hsl(200 20% 45%);
}

/* Misty Harbor Dark */
[data-theme='misty-harbor-dark'] {
  color-scheme: dark;
  --background: hsl(210 20% 10%);
  --foreground: hsl(210 15% 98%);
  --card: hsl(210 20% 12%);
  --card-foreground: hsl(210 15% 98%);
  --popover: hsl(210 20% 12%);
  --popover-foreground: hsl(210 15% 98%);
  --primary: hsl(200 20% 55%);
  --primary-foreground: hsl(200 20% 10%);
  --secondary: hsl(180 10% 35%);
  --secondary-foreground: hsl(180 10% 98%);
  --muted: hsl(210 20% 20%);
  --muted-foreground: hsl(210 15% 70%);
  --accent: hsl(240 15% 65%);
  --accent-foreground: hsl(240 15% 10%);
  --destructive: hsl(0 60% 40%);
  --destructive-foreground: hsl(0 60% 98%);
  --border: hsl(210 20% 25%);
  --input: hsl(210 20% 25%);
  --ring: hsl(200 20% 55%);
  --radius: 0.375rem;
  --chart-1: hsl(200 20% 55%);
  --chart-2: hsl(180 10% 35%);
  --chart-3: hsl(240 15% 65%);
  --chart-4: hsl(160 20% 60%);
  --chart-5: hsl(30 15% 50%);
  --sidebar: hsl(210 20% 15%);
  --sidebar-foreground: hsl(210 15% 98%);
  --sidebar-primary: hsl(200 20% 55%);
  --sidebar-primary-foreground: hsl(200 20% 10%);
  --sidebar-accent: hsl(180 10% 35%);
  --sidebar-accent-foreground: hsl(180 10% 98%);
  --sidebar-border: hsl(210 20% 30%);
  --sidebar-ring: hsl(200 20% 55%);
}

/* Pink Light */
[data-theme='pink'] {
  --background: hsl(340 100% 99%);
  --foreground: hsl(340 10% 10%);
  --card: hsl(340 100% 98%);
  --card-foreground: hsl(340 10% 10%);
  --popover: hsl(340 100% 98%);
  --popover-foreground: hsl(340 10% 10%);
  --primary: hsl(340 80% 65%);
  --primary-foreground: hsl(0 0% 98%);
  --secondary: hsl(310 70% 85%);
  --secondary-foreground: hsl(310 10% 10%);
  --muted: hsl(340 20% 90%);
  --muted-foreground: hsl(340 10% 40%);
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(30 10% 10%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(340 60% 90%);
  --input: hsl(340 60% 90%);
  --ring: hsl(340 80% 65%);
  --radius: 0.75rem;
  --chart-1: hsl(340 80% 65%);
  --chart-2: hsl(310 70% 85%);
  --chart-3: hsl(30 90% 80%);
  --chart-4: hsl(260 80% 70%);
  --chart-5: hsl(180 60% 75%);
  --sidebar: hsl(340 80% 95%);
  --sidebar-foreground: hsl(340 10% 10%);
  --sidebar-primary: hsl(340 80% 65%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(310 70% 85%);
  --sidebar-accent-foreground: hsl(310 10% 10%);
  --sidebar-border: hsl(340 60% 85%);
  --sidebar-ring: hsl(340 80% 65%);
}

/* Pink Dark */
[data-theme='pink-dark'] {
  color-scheme: dark;
  --background: hsl(340 50% 8%);
  --foreground: hsl(340 10% 95%);
  --card: hsl(340 50% 10%);
  --card-foreground: hsl(340 10% 95%);
  --popover: hsl(340 50% 10%);
  --popover-foreground: hsl(340 10% 95%);
  --primary: hsl(340 80% 75%);
  --primary-foreground: hsl(340 10% 15%);
  --secondary: hsl(310 70% 45%);
  --secondary-foreground: hsl(310 10% 95%);
  --muted: hsl(340 30% 18%);
  --muted-foreground: hsl(340 10% 70%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(30 10% 95%);
  --destructive: hsl(0 72.2% 50.6%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(340 60% 20%);
  --input: hsl(340 60% 20%);
  --ring: hsl(340 80% 75%);
  --radius: 0.75rem;
  --chart-1: hsl(340 80% 75%);
  --chart-2: hsl(310 70% 45%);
  --chart-3: hsl(30 90% 45%);
  --chart-4: hsl(260 80% 50%);
  --chart-5: hsl(180 60% 40%);
  --sidebar: hsl(340 50% 12%);
  --sidebar-foreground: hsl(340 10% 95%);
  --sidebar-primary: hsl(340 80% 75%);
  --sidebar-primary-foreground: hsl(340 10% 15%);
  --sidebar-accent: hsl(310 70% 45%);
  --sidebar-accent-foreground: hsl(310 10% 95%);
  --sidebar-border: hsl(340 60% 25%);
  --sidebar-ring: hsl(340 80% 75%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  button {
    @apply cursor-pointer;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .mention {
    color: #0070f3;
    font-weight: 700;
    background-color: rgba(0, 112, 243, 0.1);
    border-radius: 12px;
    padding: 0.2rem 0.6rem;
    margin: 0 0.1rem;
  }
  .duration-3000 {
    --tw-duration: 3000ms;
    transition-duration: var(--tw-duration);
  }
  .duration-5000 {
    --tw-duration: 5000ms;
    transition-duration: var(--tw-duration);
  }

  .delayed-fade-in {
    animation: delayedFadeIn 5s ease-in-out forwards;
    opacity: 0;
  }

  @keyframes delayedFadeIn {
    0% {
      opacity: 0;
    }
    80% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
