"use client";
import { insertProjectAction } from "@/app/api/chat/actions";
import { <PERSON>bulb, Loader } from "lucide-react";
import { useRouter } from "next/navigation";
import React, {
  KeyboardEvent,
  PropsWithChildren,
  useEffect,
  useState,
} from "react";
import { toast } from "sonner";
import { mutate } from "swr";
import { safe } from "ts-safe";
import { Button } from "ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "ui/dialog";
import { Input } from "ui/input";
import { Label } from "ui/label";
import { handleErrorWithToast } from "ui/shared-toast";
import { useTranslations } from "next-intl";

export function CreateProjectPopup({ children }: PropsWithChildren) {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState("");
  const router = useRouter();

  const handleCreate = async () => {
    safe(() => setIsLoading(true))
      .map(() => insertProjectAction({ name }))
      .watch(() => setIsLoading(false))
      .ifOk(() => setIsOpen(false))
      .ifOk(() => toast.success(t("Chat.Project.projectCreated")))
      .ifOk(() => mutate("projects"))
      .ifOk((project) => router.push(`/project/${project.id}`))
      .ifFail(handleErrorWithToast);
  };

  const handleEnterKey = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.nativeEvent.isComposing) {
      handleCreate();
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setName("");
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-card">
        <DialogHeader>
          <DialogTitle>{t("Chat.Project.project")}</DialogTitle>
          <DialogDescription asChild>
            <div className="my-2 p-4 flex bg-muted rounded-lg gap-2">
              <div className="px-2">
                <Lightbulb className="size-4 text-accent-foreground animate-pulse" />
              </div>
              <div className="">
                <p className="font-semibold text-accent-foreground mb-1">
                  {t("Chat.Project.whatIsAProject")}
                </p>
                {t(
                  "Chat.Project.aProjectAllowsYouToOrganizeYourFilesAndCustomInstructionsInOneConvenientPlace",
                )}
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center gap-2 w-full">
          <Label htmlFor="name">{t("Chat.Project.projectName")}</Label>
          <Input
            autoFocus
            id="name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            onKeyDown={handleEnterKey}
            placeholder={t("Chat.Project.enterNameForNewProject")}
            className="w-full bg-card"
          />
        </div>
        <DialogFooter>
          <DialogClose asChild disabled={isLoading}>
            <Button variant="ghost">{t("Common.cancel")}</Button>
          </DialogClose>
          <Button
            type="submit"
            disabled={isLoading || !name.trim()}
            onClick={handleCreate}
            variant={"secondary"}
          >
            {isLoading && <Loader className="size-4 animate-spin" />}
            {t("Common.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
